import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { CoordinatorDurableObjBrowserStateRepository } from '../src/workflow/CoordinatorDurableObjBrowserStateRepository';
import { BrowserState } from '../src/workflow/types/BrowserState';
import { PlatformTypes } from '../src/ui/constants';
import { UserSessionData } from '../src/shared/coordinator-types';

describe('CoordinatorDurableObjBrowserStateRepository Tests', () => {
  let repository: CoordinatorDurableObjBrowserStateRepository;
  let mockCoordinatorDO: any;
  let mockSessionData: Record<string, UserSessionData>;

  beforeEach(() => {
    // Reset mock session data
    mockSessionData = {};

    // Create mock CoordinatorDO stub
    mockCoordinatorDO = {
      getSessionData: vi.fn().mockImplementation((platformId: PlatformTypes, userId: string) => {
        const key = `${platformId}-${userId}`;
        return Promise.resolve(mockSessionData[key] || null);
      }),
      updateSessionData: vi
        .fn()
        .mockImplementation(
          (platformId: PlatformTypes, userId: string, sessionData: UserSessionData) => {
            const key = `${platformId}-${userId}`;
            mockSessionData[key] = sessionData;
            return Promise.resolve();
          },
        ),
      clearSessionData: vi.fn().mockImplementation((platformId: PlatformTypes, userId: string) => {
        const key = `${platformId}-${userId}`;
        delete mockSessionData[key];
        return Promise.resolve();
      }),
    };

    repository = new CoordinatorDurableObjBrowserStateRepository(mockCoordinatorDO);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  const cookieExample = {
    name: 'session_id',
    value: 'abc123xyz789',
    domain: 'example.com',
    path: '/',
    expires: '883612800',
    secure: 'true',
    httpOnly: 'true',
    sameSite: 'Lax' as const,
    size: '25',
    session: 'false',
  };

  const sessionStorageExample: Record<string, string> = {
    'soft-nav:marker': '0',
  };

  const localStorageExample: Record<string, string> = {
    user: JSON.stringify({
      id: 'u12345',
      name: 'Jane Doe',
      email: '<EMAIL>',
    }),
    theme: 'dark',
    language: 'en-US',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  };

  const createBrowserState = (
    userId = 'test_user',
    platform = PlatformTypes.FACEBOOK,
  ): BrowserState => ({
    userId,
    platform,
    cookies: [cookieExample],
    localStorageData: localStorageExample,
    sessionStorageData: sessionStorageExample,
  });

  it('should store and retrieve browser state', async () => {
    // Arrange
    const browserState = createBrowserState();

    // Act
    const storedState = await repository.updateBrowserState(browserState);
    const retrievedState = await repository.getBrowserState(
      browserState.userId,
      browserState.platform,
    );

    // Assert
    expect(storedState).toEqual(browserState);
    expect(retrievedState).toEqual(browserState);
    expect(mockCoordinatorDO.updateSessionData).toHaveBeenCalledWith(
      PlatformTypes.FACEBOOK,
      'test_user',
      {
        browserState: {
          cookies: [cookieExample],
          localStorageData: localStorageExample,
          sessionStorageData: sessionStorageExample,
        },
        lastUpdated: expect.any(Number),
      },
    );
  });

  it('should return null for non-existent browser state', async () => {
    // Act
    const result = await repository.getBrowserState('non_existent_user', PlatformTypes.FACEBOOK);

    // Assert
    expect(result).toBeNull();
    expect(mockCoordinatorDO.getSessionData).toHaveBeenCalledWith(
      PlatformTypes.FACEBOOK,
      'non_existent_user',
    );
  });

  it('should return null when sessionData exists but browserState is missing', async () => {
    // Arrange
    const userId = 'test_user';
    const platform = PlatformTypes.FACEBOOK;
    mockSessionData[`${platform}-${userId}`] = {
      lastUpdated: Date.now(),
      // No browserState property
    };

    // Act
    const result = await repository.getBrowserState(userId, platform);

    // Assert
    expect(result).toBeNull();
  });

  it('should delete browser state', async () => {
    // Arrange
    const browserState = createBrowserState();
    await repository.updateBrowserState(browserState);

    // Verify state exists
    const stateBefore = await repository.getBrowserState(
      browserState.userId,
      browserState.platform,
    );
    expect(stateBefore).not.toBeNull();

    // Act
    await repository.deleteBrowserState(browserState.userId, browserState.platform);

    // Assert
    const stateAfter = await repository.getBrowserState(browserState.userId, browserState.platform);
    expect(stateAfter).toBeNull();
    expect(mockCoordinatorDO.clearSessionData).toHaveBeenCalledWith(
      PlatformTypes.FACEBOOK,
      'test_user',
    );
  });

  it('should handle multiple users on the same platform', async () => {
    // Arrange
    const user1State = createBrowserState('user1', PlatformTypes.FACEBOOK);
    const user2State = createBrowserState('user2', PlatformTypes.FACEBOOK);

    // Act
    await repository.updateBrowserState(user1State);
    await repository.updateBrowserState(user2State);

    // Assert
    const retrievedUser1 = await repository.getBrowserState('user1', PlatformTypes.FACEBOOK);
    const retrievedUser2 = await repository.getBrowserState('user2', PlatformTypes.FACEBOOK);

    expect(retrievedUser1).toEqual(user1State);
    expect(retrievedUser2).toEqual(user2State);
  });

  it('should handle multiple platforms for the same user', async () => {
    // Arrange
    const facebookState = createBrowserState('user1', PlatformTypes.FACEBOOK);
    const twitterState = createBrowserState('user1', PlatformTypes.GOOGLE);

    // Act
    await repository.updateBrowserState(facebookState);
    await repository.updateBrowserState(twitterState);

    // Assert
    const retrievedFacebook = await repository.getBrowserState('user1', PlatformTypes.FACEBOOK);
    const retrievedTwitter = await repository.getBrowserState('user1', PlatformTypes.GOOGLE);

    expect(retrievedFacebook).toEqual(facebookState);
    expect(retrievedTwitter).toEqual(twitterState);
  });

  it('should handle empty browser state data gracefully', async () => {
    // Arrange
    const emptyBrowserState: BrowserState = {
      userId: 'test_user',
      platform: PlatformTypes.FACEBOOK,
      cookies: [],
      localStorageData: {},
      sessionStorageData: {},
    };

    // Act
    await repository.updateBrowserState(emptyBrowserState);
    const retrieved = await repository.getBrowserState('test_user', PlatformTypes.FACEBOOK);

    // Assert - should return null for empty data
    expect(retrieved).toBeNull();
  });

  it('should handle errors gracefully', async () => {
    // Arrange
    mockCoordinatorDO.getSessionData.mockRejectedValue(new Error('Network error'));

    // Act
    const result = await repository.getBrowserState('test_user', PlatformTypes.FACEBOOK);

    // Assert
    expect(result).toBeNull();
  });

  it('should propagate errors on update failures', async () => {
    // Arrange
    const browserState = createBrowserState();
    mockCoordinatorDO.updateSessionData.mockRejectedValue(new Error('Update failed'));

    // Act & Assert
    await expect(repository.updateBrowserState(browserState)).rejects.toThrow('Update failed');
  });

  it('should propagate errors on delete failures', async () => {
    // Arrange
    mockCoordinatorDO.clearSessionData.mockRejectedValue(new Error('Delete failed'));

    // Act & Assert
    await expect(
      repository.deleteBrowserState('test_user', PlatformTypes.FACEBOOK),
    ).rejects.toThrow('Delete failed');
  });

  it('should read a user browser state bundle', async () => {
    // Arrange
    const ourUser = 'u_userndkfdogh328943';
    const facebookState = createBrowserState(ourUser, PlatformTypes.FACEBOOK);
    const googleState = createBrowserState(ourUser, PlatformTypes.GOOGLE);

    // Act
    await repository.updateBrowserState(facebookState);
    await repository.updateBrowserState(googleState);

    // Assert
    const browserStateBundle = await repository.getBrowserStateBundle(ourUser);

    expect(browserStateBundle.platformBrowserStates.length).toEqual(2);

    const retrievedFacebook = browserStateBundle.platformBrowserStates.find(
      (state) => state.platform === PlatformTypes.FACEBOOK,
    );
    const retrievedGoogle = browserStateBundle.platformBrowserStates.find(
      (state) => state.platform === PlatformTypes.GOOGLE,
    );

    expect(retrievedFacebook).toEqual(facebookState);
    expect(retrievedGoogle).toEqual(googleState);
  });
});
