import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { fetchMock, env } from 'cloudflare:test';
import { BrowserStateService } from '../src/workflow/BrowserStateService';
import { BrowserStateRepository } from '../src/workflow/types/BrowserStateRepository';
import { R2BrowserStateRepository } from '../src/workflow/R2BrowserStateRepository';
import { MockBrowserDataAdapter } from './mocks/MockBrowserDataAdapter';
import { PlatformTypes } from '../src/ui/constants';

describe('Browser State Tests', () => {
  let browserStateRepository: BrowserStateRepository;
  let browserStateService: BrowserStateService;

  beforeEach(() => {
    fetchMock.activate();

    // Use the real R2 bucket from the environment
    browserStateRepository = new R2BrowserStateRepository(env.SCREENSHOTS_INBOUND_BUCKET);
    browserStateService = new BrowserStateService(browserStateRepository);
  });

  afterEach(async () => {
    fetchMock.deactivate();
    vi.clearAllMocks();
  });

  const cookieExample = {
    name: 'session_id',
    value: 'abc123xyz789',
    domain: 'example.com',
    path: '/',
    expires: 883612800,
    secure: true,
    httpOnly: true,
    sameSite: 'Lax' as const,
    size: 25,
    session: false,
  };

  const sessionStorageExample: Record<string, string> = {
    'soft-nav:marker': '0',
  };

  const localStorageExample: Record<string, string> = {
    user: JSON.stringify({
      id: 'u12345',
      name: 'Jane Doe',
      email: '<EMAIL>',
    }),
    theme: 'dark',
    language: 'en-US',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  };

  // Helper function to create mock adapter with test data
  const createMockAdapter = (
    cookies = [cookieExample],
    localStorage = localStorageExample,
    sessionStorage = sessionStorageExample,
  ) => {
    return new MockBrowserDataAdapter(cookies, localStorage, sessionStorage);
  };

  it('should store and read the browser state object from a page', async () => {
    // Arrange
    const userId = 'u_123';
    const platform = PlatformTypes.FACEBOOK;
    const mockAdapter = createMockAdapter();

    // Act
    const browserState = await browserStateService.updateBrowserState(
      mockAdapter,
      userId,
      platform,
    );

    // Assert
    expect(browserState.userId).toBe(userId);
    expect(browserState.platform).toBe(platform);
    expect(browserState.cookies).toEqual([cookieExample]);
    expect(browserState.localStorageData).toEqual(localStorageExample);
    expect(browserState.sessionStorageData).toEqual(sessionStorageExample);

    // Verify we can retrieve the state
    const retrievedState = await browserStateService.getBrowserState(userId, platform);
    expect(retrievedState?.userId).toBe(userId);
    expect(retrievedState?.platform).toBe(platform);
    expect(retrievedState?.cookies).toStrictEqual([cookieExample]);
    expect(retrievedState?.localStorageData).toStrictEqual(localStorageExample);
    expect(retrievedState?.sessionStorageData).toStrictEqual(sessionStorageExample);
  });

  it('should update a browser state object', async () => {
    // Arrange
    const userId = 'u_456';
    const platform = PlatformTypes.FACEBOOK;

    // Set the initial page state
    const initialAdapter = createMockAdapter();
    await browserStateService.updateBrowserState(initialAdapter, userId, platform);

    // Create updated data
    const updatedCookie = {
      name: 'updated_session',
      value: 'new_value_123',
      domain: 'example.com',
      path: '/',
      expires: 883612800,
      secure: true,
      httpOnly: true,
      sameSite: 'Lax' as const,
      size: 30,
      session: false,
    };

    const updatedLocalStorage = {
      ...localStorageExample,
      theme: 'light',
      newSetting: 'enabled',
    };

    // Create adapter with updated data
    const updatedAdapter = createMockAdapter(
      [updatedCookie],
      updatedLocalStorage,
      sessionStorageExample,
    );

    // Act
    await browserStateService.updateBrowserState(updatedAdapter, userId, platform);
    const updatedState = await browserStateService.getBrowserState(userId, platform);

    // Assert
    expect(updatedState?.userId).toBe(userId);
    expect(updatedState?.platform).toBe(platform);

    expect(updatedState?.cookies).toStrictEqual([updatedCookie]);
    expect(updatedState?.localStorageData).toStrictEqual(updatedLocalStorage);

    // Session storage not updated
    expect(updatedState?.sessionStorageData).toStrictEqual(sessionStorageExample);
  });

  it('should be able to delete a browser state object', async () => {
    // Arrange
    const userId = 'delete_test_user';
    const platform = PlatformTypes.FACEBOOK;

    // Create the state
    const mockAdapter = createMockAdapter();
    await browserStateService.updateBrowserState(mockAdapter, userId, platform);

    // Verify the state exists
    const stateBefore = await browserStateService.getBrowserState(userId, platform);
    expect(stateBefore?.cookies).toBeTruthy();

    // Act
    await browserStateService.deleteBrowserState(userId, platform);

    // Get the state after deletion
    const stateAfter = await browserStateService.getBrowserState(userId, platform);
    expect(stateAfter).toBe(null);
  });

  it('should handle non-existent browser state', async () => {
    // Arrange
    const userId = 'non_existent_user';
    const platform = PlatformTypes.TEXT_CAPTCHA;

    // Make sure the state doesn't exist
    await browserStateService.deleteBrowserState(userId, platform);

    // Act
    const result = await browserStateService.getBrowserState(userId, platform);

    // Assert
    expect(result).toBe(null);
  });

  it('should handle property name mismatch between interface and implementation', async () => {
    // Arrange
    const userId = 'test_user';
    const platform = PlatformTypes.FACEBOOK;

    // Act - call the service method that should handle the property name mismatch
    const mockAdapter = createMockAdapter();
    const result = await browserStateService.updateBrowserState(mockAdapter, userId, platform);

    // Assert - verify the property was added
    expect(result.sessionStorageData).toBeDefined();

    // The implementation adds sessionStorage property
    const retrievedState = await browserStateService.getBrowserState(userId, platform);
    expect(retrievedState?.sessionStorageData).toBeDefined();
  });
});
