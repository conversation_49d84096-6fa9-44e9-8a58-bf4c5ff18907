/**
 * Response schemas for LLM calls
 */

/**
 * Schema for the first/extraction call - extracts form controls and metadata
 */
export const EXTRACTION_RESPONSE_SCHEMA = {
  type: 'object',
  description:
    'Result of the rule‑based filtering task. Contains only the UI elements that remain after all rules are applied.',
  properties: {
    screenInfo: {
      type: ['object', 'null'],
      description:
        'High‑level metadata about the analysed frame. Place this object first in the output payload.',
      properties: {
        controlVisibilityRules: {
          type: 'array',
          description: 'Audit list of every detected control and its filtering outcome.',
          items: {
            type: 'object',
            properties: {
              id: {
                type: 'string',
                description: "The control's unique identifier. Short and human‑readable.",
              },
              status: {
                type: 'string',
                enum: ['included', 'excluded'],
                description: 'Whether the control survived the filtering rules.',
              },
              reason: {
                type: ['string', 'null'],
                description: 'Summarized reason for inclusion or exclusion.',
              },
            },
            required: ['id', 'status'],
          },
        },
        errors: {
          type: ['array', 'null'],
          items: { type: 'string' },
          description: 'A list of all visible validation error messages or security notices.',
        },
      },
      required: ['errors'],
    },

    controls: {
      type: 'object',
      description: 'Interactive elements.',
      properties: {
        fields: {
          type: 'array',
          description: 'Input‑type controls kept after filtering.',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              order: { type: 'number', minimum: 1 },
              label: { type: 'string' },
              isLikelyDropdownReason: {
                type: 'string',
                description:
                  'Is a chevron icon or dropdown arrow part of the control or nearby the control? Explain your reasoning.',
              },
              isLikelyDropdown: {
                type: 'boolean',
                description:
                  'Is a chevron icon or dropdown arrow part of the control or nearby the control? If so, set this to true.',
              },
              fieldControlType: {
                type: 'string',
                enum: [
                  'dropdown',
                  'select',
                  'text',
                  'password',
                  'number',
                  'checkbox',
                  'checkboxgroup',
                  'radiogroup',
                  'textarea',
                  'other',
                ],
                description:
                  'Type of the input control. If the control has a dropdown arrow, set the \`fieldControlType\` as \`dropdown\`. \'radiogroup\' represents a set of mutually exclusive options.',
              },
              actiontype: { type: 'string', enum: ['fill', 'select'] },
              name: { type: 'string' },
              options: {
                type: ['array', 'null'],
                items: {
                  type: 'object',
                  properties: {
                    value: { type: 'string' },
                    label: { type: 'string' },
                  },
                  required: ['value', 'label'],
                },
              },
              checked: { type: 'boolean' },
              isDontAskAgainControl: {
                type: 'boolean',
                description:
                  'Is the control allowing the user to remain authenticated or have their device remembered or trusted?.',
              },
            },
            required: [
              'isLikelyDropdownReason',
              'isLikelyDropdown',
              'id',
              'order',
              'label',
              'fieldControlType',
              'actiontype',
              'options',
              'name',
              'checked',
              'isDontAskAgainControl',
            ],
            propertyOrdering: [
              'isLikelyDropdownReason',
              'id',
              'order',
              'label',
              'isLikelyDropdown',
              'fieldControlType',
              'actiontype',
              'options',
              'name',
              'checked',
              'isDontAskAgainControl',
            ],
          },
        },

        buttons: {
          type: 'array',
          description: 'Button‑type controls kept after filtering.',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              order: { type: 'number', minimum: 1 },
              label: { type: 'string' },
              variant: { type: 'string', enum: ['primary', 'secondary', 'link'] },
              type: { type: 'string', enum: ['submit', 'click', 'device-ack'] },
              actiontype: { type: 'string', enum: ['click'] },
              isDontAskAgainControl: {
                type: 'boolean',
                description:
                  'Is the control allowing the user to remain authenticated or have their device remembered or trusted?.',
              },
            },
            required: [
              'id',
              'order',
              'label',
              'variant',
              'type',
              'actiontype',
              'isDontAskAgainControl',
            ],
            propertyOrdering: [
              'id',
              'order',
              'label',
              'variant',
              'type',
              'actiontype',
              'isDontAskAgainControl',
            ],
          },
        },
      },
      required: ['fields', 'buttons'],
      propertyOrdering: ['fields', 'buttons'],
    },
  },

  required: ['screenInfo', 'controls'],
  propertyOrdering: ['screenInfo', 'controls'],
} as const;

/**
 * Schema for the second/classification call - provides screen classification and verification codes
 */
export const CLASSIFICATION_RESPONSE_SCHEMA = {
  type: 'object',
  description:
    'Result of the rule‑based filtering task. Contains only the UI elements that remain after all rules are applied.',
  properties: {
    screenInfo: {
      type: ['object'],
      description:
        'High-level metadata identifying the primary classification and context of the screen.',
      properties: {
        classificationReasoning: {
          type: 'string',
          description:
            "A concise explanation for why the screen was assigned its 'screenClass'. This reasoning must be based on visible evidence from the image, such as specific text or UI patterns (e.g., 'The screen contains a reCAPTCHA element' or 'The presence of a password change form indicates profile management').",
        },
        authStateReasoning: {
          type: 'string',
          description:
            "A concise explanation for why the screen was assigned its 'authState'. This reasoning must be based on visible evidence from the image, such as specific text or UI patterns.",
        },
        screenClass: {
          type: 'string',
          enum: [
            'profile-management-screen',
            'multi-factor-push-approval-screen',
            'multi-factor-code-verification-screen',
            'multi-factor-multiple-options-screen',
            'passkey-screen',
            'captcha-screen',
            'loading-screen',
            'logged-in-screen',
            'trust-device-screen',
            'other',
          ],
          description:
            "The screen's single, primary classification, determined by applying a prioritized list of rules. This identifies the screen's main function.",
        },
        title: {
          type: 'string',
        },
        instruction: {
          type: ['string'],
          description: 'Actionable guidance for the user. Must not be empty.',
        },
        authState: {
          type: 'string',
          description: 'Authentication status of the user with respect to the current screen.',
          enum: ['authenticated', 'not-authenticated'],
        },
        screenCodeReasoning: {
          type: ['string'],
          description: 'What is the code or number provided to the user? Explain your reasoning.',
        },
        screenCode: {
          type: ['number', 'null'],
          description:
            'Extract a code or number provided to the user that needs to be entered or verified on a different device or application. This code is typically used for multi-factor authentication or verification purposes.',
        },
        alertsCount: {
          type: ['number', 'null'],
          description: 'The number of visible alerts on the screen.',
        },
        alertsAnalysis: {
          type: 'string',
          description:
            'In one or two sentences, describe any text on the screen that appears to be an **error, warning, notice, or security alert.** Ignore purely informational text or standard success messages.',
        },
        alerts: {
          type: ['array', 'null'],
          description:
            '**Based on the `alertsAnalysis`**, populate this array with the structured details of each identified alert. If the analysis found no alerts, this should be an empty array.',
          items: {
            type: 'object',
            properties: {
              alertText: {
                type: 'string',
                description: 'The exact, verbatim text of the message.',
              },
              alertType: {
                type: 'string',
                description:
                  "The classification of the alert. Must be 'error', 'warning', 'notice', or 'security'.",
                enum: ['error', 'warning', 'security', 'notice'],
              },
            },
          },
        },
        alertsSummary: {
          type: 'string',
          description:
            'Provide a single, concise, user-friendly summary of the situation or condition. Use second person ("you") and present tense. Write a short, plain-language sentence that directly states the cause of the error, warning, notice, or security messages. No instructions or next steps. Only describe the situation or condition.',
        },
      },
      required: [
        'title',
        'classificationReasoning',
        'authStateReasoning',
        'authState',
        'instruction',
        'screenClass',
        'alertsCount',
        'alertsAnalysis',
        'alerts',
        'alertsSummary',
        'screenCodeReasoning',
        'screenCode',
      ],
      propertyOrdering: [
        'classificationReasoning',
        'authStateReasoning',
        'screenClass',
        'instruction',
        'title',
        'authState',
        'alertsCount',
        'alertsAnalysis',
        'alerts',
        'alertsSummary',
        'screenCodeReasoning',
        'screenCode',
      ],
    },
  },

  required: ['screenInfo'],
  propertyOrdering: ['screenInfo'],
} as const;
