import { BrowserStateRepository } from './types/BrowserStateRepository';
import { BrowserState, BrowserStateBundle } from './types/BrowserState';
import { PlatformTypes } from '../ui/constants';
import { UserSessionData } from '../shared/coordinator-types';

/**
 * Interface for CoordinatorDO RPC methods used by the browser state repository
 */
interface CoordinatorDOStub {
  getSessionData(platformId: PlatformTypes, userId: string): Promise<UserSessionData | null>;
  updateSessionData(
    platformId: PlatformTypes,
    userId: string,
    sessionData: UserSessionData,
  ): Promise<void>;
  clearSessionData(platformId: PlatformTypes, userId: string): Promise<void>;
}

/**
 * Browser state repository that stores browser state in CoordinatorDO's sessionData
 *
 * This implementation stores browser state (cookies, localStorage, sessionStorage)
 * within the PlatformMetadata.sessionData field of the CoordinatorDO state.
 */
export class CoordinatorDurableObjBrowserStateRepository implements BrowserStateRepository {
  private coordinatorDO: CoordinatorDOStub;

  constructor(coordinatorDO: CoordinatorDOStub) {
    this.coordinatorDO = coordinatorDO;
  }

  /**
   * Retrieve browser state for a user and platform from CoordinatorDO sessionData
   */
  async getBrowserState(userId: string, platform: PlatformTypes): Promise<BrowserState | null> {
    try {
      const sessionData = await this.coordinatorDO.getSessionData(
        platform as PlatformTypes,
        userId,
      );

      if (!sessionData || !sessionData.browserState) {
        return null;
      }

      const { cookies, localStorageData, sessionStorageData } = sessionData.browserState;

      if (!cookies && !localStorageData && !sessionStorageData) {
        return null;
      }

      return {
        platform,
        userId,
        cookies: cookies || [],
        localStorageData: localStorageData || {},
        sessionStorageData: sessionStorageData || {},
      };
    } catch (error) {
      console.error(
        `[CoordinatorDurableObjBrowserStateRepository] Error getting browser state for ${userId}/${platform}:`,
        error,
      );
      return null;
    }
  }

  async getBrowserStateBundle(userId: string): Promise<BrowserStateBundle> {
    const sessions: BrowserState[] = [];

    for (const platform of Object.values(PlatformTypes)) {
      const result = await this.getBrowserState(userId, platform);
      if (result !== null) {
        sessions.push(result);
      }
    }

    return {
      platformBrowserStates: sessions,
    };
  }

  /**
   * Update browser state for a user and platform in CoordinatorDO sessionData
   */
  async updateBrowserState(browserState: BrowserState): Promise<BrowserState> {
    try {
      await this.coordinatorDO.updateSessionData(
        browserState.platform as PlatformTypes,
        browserState.userId,
        {
          browserState: {
            cookies: browserState.cookies,
            localStorageData: browserState.localStorageData,
            sessionStorageData: browserState.sessionStorageData,
          },
          lastUpdated: Date.now(),
        },
      );

      return browserState;
    } catch (error) {
      console.error(
        `[CoordinatorDurableObjBrowserStateRepository] Error updating browser state for ${browserState.userId}/${browserState.platform}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Delete browser state for a user and platform from CoordinatorDO sessionData
   */
  async deleteBrowserState(userId: string, platform: string): Promise<void> {
    try {
      await this.coordinatorDO.clearSessionData(platform as PlatformTypes, userId);
    } catch (error) {
      console.error(
        `[CoordinatorDurableObjBrowserStateRepository] Error deleting browser state for ${userId}/${platform}:`,
        error,
      );
      throw error;
    }
  }
}
