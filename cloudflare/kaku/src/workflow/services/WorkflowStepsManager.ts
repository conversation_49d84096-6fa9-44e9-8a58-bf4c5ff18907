import { ConnectionsWorkflowParams } from '../types';
import { WorkflowStepName } from '../types/WorkflowStepName';
import { ErrorService } from '../../common/error';
import { WorkflowStep, WorkflowStepConfig } from 'cloudflare:workers';
import { defaultWorkflowNoRetryConfig } from '../utils/constants';
import { FormSubmissionEvent } from '../types/ConnectionsWorkflowParams';

type ErrorHandler = (error: any) => Promise<void>;

export class WorkflowStepsManager {
  private readonly env: Env;
  private readonly eventPayload: ConnectionsWorkflowParams;
  private readonly step: WorkflowStep;
  private readonly errorHandler: ErrorHandler;

  constructor({
    env,
    step,
    eventPayload,
    errorHandler,
  }: {
    env: Env;
    step: WorkflowStep;
    eventPayload: ConnectionsWorkflowParams;
    errorHandler: ErrorHandler;
  }) {
    this.step = step;
    this.eventPayload = eventPayload;
    this.env = env;
    this.errorHandler = errorHandler;
  }

  async tryRunStep<S extends Rpc.Serializable<S>>(
    workflowStepName: WorkflowStepName,
    stepCallback: () => Promise<S>,
    retryConfig: WorkflowStepConfig = defaultWorkflowNoRetryConfig,
  ): Promise<S> {
    try {
      return await this.runStep(workflowStepName, stepCallback, retryConfig);
    } catch (e) {
      await this.handleWorkflowStepError(workflowStepName, e);
      throw e;
    }
  }

  async runStep<S extends Rpc.Serializable<S>>(
    workflowStepName: WorkflowStepName,
    stepCallback: () => Promise<S>,
    retryConfig: WorkflowStepConfig,
  ): Promise<S> {
    return await this.step.do(workflowStepName, retryConfig, stepCallback);
  }

  async waitForCaptchaSolvedEvent() {
    return await this.step.waitForEvent<{
      differencePercentage: number;
      timestamp: string;
      source?: string;
    }>('Await captcha solved', {
      type: 'captcha-solved',
      timeout: '3 minutes',
    });
  }

  async waitForFormSubmittedEvent() {
    return await this.step.waitForEvent<FormSubmissionEvent>(
      WorkflowStepName.AWAIT_USER_FORM_INPUT,
      {
        type: 'form-submission',
        timeout: '10 minutes',
      },
    );
  }

  private async handleWorkflowStepError(
    workflowStepName: WorkflowStepName,
    error: any,
  ): Promise<void> {
    await ErrorService.handleWorkflowStepError(
      workflowStepName,
      error,
      {
        ...this.eventPayload,
        referenceId: this.eventPayload.linkId,
      },
      this.env,
    );
    await this.errorHandler(error);
  }
}
