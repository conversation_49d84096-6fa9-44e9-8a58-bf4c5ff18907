import { BrowserState, Browser<PERSON>tateBundle } from './BrowserState';
import { PlatformTypes } from '../../ui/constants';

export interface BrowserStateRepository {
  getBrowserState(userId: string, platform: PlatformTypes): Promise<BrowserState | null>;

  getBrowserStateBundle(userId: string): Promise<BrowserStateBundle | null>;

  updateBrowserState(browserState: BrowserState): Promise<BrowserState>;

  deleteBrowserState(userId: string, platform: PlatformTypes): Promise<void>;
}
