export enum WorkflowStepName {
  SETUP_VIEWPORT_AND_DEVICE_METRICS = 'setup viewport and device metrics',
  LOAD_BROWSER_SESSIONS = 'load browser sessions',
  SETUP_BROWSER_SESSION = 'setup browser session',
  INITIALIZE_SESSION = 'Initialize session',
  CAPTURE_SCREENSHOT = 'Capture screenshot',
  GENERATE_FORM_WITH_AI = 'Generate form with OpenAI',
  SCREENSHOT_AFTER_LOADING = 'Capture screenshot after loading',
  INJECT_SCRIPT = 'Inject scripts for two-tab architecture',
  SEND_CAPTCHA_DETECTION_RESPONSE = 'Send captcha detection to agent',
  PAUSE_INTERACTIVITY = 'pause interactivity',
  VERIFY_CAPTCHA_STATUS = 'Verify captcha status',
  ACKNOWLEDGE_EXTRACTED_FORM = 'Acknowledge extracted form',
  TAKE_SCREENSHOT = 'Take screenshot',
  AWAIT_USER_FORM_INPUT = 'Await user form input',
  EXECUTE_FORM_ACTIONS = 'Execute form actions',
  WAIT_FOR_PAGE_UPDATE_AFTER_SUBMISSION = 'Wait for page update after submission',
  WAIT_FOR_PAGE_UPDATE_AFTER_CAPTCHA_COMPLETION = 'Wait for page update after captcha completion',
  GENERATE_FORM_HTMX = 'Generate form HTMX',
  GENERATE_FORM_FIELDS = 'Generate form fields',
}
