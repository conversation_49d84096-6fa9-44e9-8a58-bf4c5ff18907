import { <PERSON><PERSON> } from 'hono/utils/cookie';
import { PlatformTypes } from '../../ui/constants';

export interface BrowserState {
  platform: PlatformTypes;
  userId: string;
  cookies: <PERSON>ie[];
  localStorageData: Record<string, string | null>;
  sessionStorageData: Record<string, string | null>;
  lastUpdated?: number;
}

export interface BrowserStateBundle {
  platformBrowserStates: BrowserState[];
}
