import { BrowserStateRepository } from './types/BrowserStateRepository';
import { BrowserState, BrowserStateBundle } from './types/BrowserState';
import { PlatformTypes } from '../ui/constants';

export class R2BrowserStateRepository implements BrowserStateRepository {
  private bucket: R2Bucket;

  constructor(bucket: R2Bucket) {
    this.bucket = bucket;
  }
  async getBrowserStateBundle(userId: string): Promise<BrowserStateBundle> {
    const sessions: BrowserState[] = [];

    for (const platform of Object.values(PlatformTypes)) {
      const result = await this.getBrowserState(userId, platform);
      if (result !== null) {
        sessions.push(result);
      }
    }

    return {
      platformBrowserStates: sessions,
    };
  }

  async deleteBrowserState(userId: string, platform: PlatformTypes): Promise<void> {
    const keyPath = this.generateStateKeyPath(userId, platform);

    let cursor: string | undefined;
    do {
      const objects = await this.bucket.list({
        prefix: keyPath,
        cursor,
      });

      await Promise.all(objects.objects.map((object) => this.bucket.delete(object.key)));

      cursor = objects.truncated ? objects.cursor : undefined;
    } while (cursor);
  }

  async getBrowserState(userId: string, platform: PlatformTypes): Promise<BrowserState | null> {
    const keyPath = this.generateStateKeyPath(userId, platform);

    const cookies = await this.bucket.get(`${keyPath}cookies`);
    const localStorage = await this.bucket.get(`${keyPath}localStorage`);
    const sessionStorage = await this.bucket.get(`${keyPath}sessionStorage`);
    if (!cookies && !localStorage && !sessionStorage) {
      return null;
    }
    return {
      platform,
      userId,
      cookies: JSON.parse((await cookies?.text()) ?? '{}'),
      localStorageData: JSON.parse((await localStorage?.text()) ?? '{}'),
      sessionStorageData: JSON.parse((await sessionStorage?.text()) ?? '{}'),
    };
  }

  async updateBrowserState(browserState: BrowserState): Promise<BrowserState> {
    const keyPath = this.generateStateKeyPath(browserState.userId, browserState.platform);

    const serializedCookies = JSON.stringify(browserState.cookies);
    const serializedSessionStorage = JSON.stringify(browserState.sessionStorageData);
    const serializedLocalStorageData = JSON.stringify(browserState.localStorageData);

    await this.bucket.put(`${keyPath}cookies`, serializedCookies);
    await this.bucket.put(`${keyPath}localStorage`, serializedLocalStorageData);
    await this.bucket.put(`${keyPath}sessionStorage`, serializedSessionStorage);

    return browserState;
  }

  private generateStateKeyPath(userId: string, platform: string): string {
    return `browser_state/${userId}/${platform}/`;
  }
}
